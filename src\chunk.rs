use bevy::{asset::RenderAssetUsages, platform::collections::HashMap, prelude::*, render::mesh::PrimitiveTopology};
use libnoise::prelude::*;

pub struct ChunkPlugin;

#[derive(Debug, Component)]
pub struct Chunk {
    data: [[[u8; 32]; 32]; 32],
    position: IVec3,
    left_chunk_position: Option<IVec3>,
    right_chunk_position: Option<IVec3>,
    front_chunk_position: Option<IVec3>,
    back_chunk_position: Option<IVec3>,
    up_chunk_position: Option<IVec3>,
    down_chunk_position: Option<IVec3>,
    mesh: Option<Mesh>,
}

impl Chunk {
    pub fn generate_chunk(position: IVec3, seed: u32) -> Chunk {
        let start_pos = position * 32;
        let end_pos = (position + 1) * 32;
        let mut data = [[[0; 32]; 32]; 32];

        let noise = Source::perlin(seed as u64)
            .translate([start_pos.x as f64 * 10.0, start_pos.z as f64 * 10.0])
            .scale([0.1, 0.1])
            .mul(64.0);
        let heightmap = NoiseBuffer::<2>::new([32, 32], &noise);

        for x in start_pos.x..end_pos.x {
            for y in start_pos.y..end_pos.y {
                for z in start_pos.z..end_pos.z {
                    let height = heightmap[[(x - start_pos.x) as usize, (z - start_pos.z) as usize]];
                    data[(x - start_pos.x) as usize][(y - start_pos.y) as usize][(z - start_pos.z) as usize] 
                        = if y > height as i32 { 0 } else { 1 };
                }
            }
        }
        Chunk {
            data,
            position,
            left_chunk_position: None,
            right_chunk_position: None,
            front_chunk_position: None,
            back_chunk_position: None,
            up_chunk_position: None,
            down_chunk_position: None,
            mesh: None,
        }
    }

    pub fn has_mesh(&self) -> bool {
        self.mesh.is_some()
    }

    pub fn get_adjacent_positions(&self) -> (Option<IVec3>, Option<IVec3>, Option<IVec3>, Option<IVec3>, Option<IVec3>, Option<IVec3>) {
        (
            self.down_chunk_position,
            self.up_chunk_position,
            self.left_chunk_position,
            self.right_chunk_position,
            self.front_chunk_position,
            self.back_chunk_position,
        )
    }

    pub fn get_data(&self) -> [[[u8; 32]; 32]; 32] {
        self.data
    }

    pub fn get_position(&self) -> IVec3 {
        self.position
    }

    pub fn get_mesh(&self) -> Option<Mesh> {
        self.mesh.clone()
    }

    pub fn add_adjacent_chunk_pointers(chunk: &mut Chunk, chunks: &HashMap<IVec3, Chunk>) {
        let left_pos = chunk.position - IVec3::new(1, 0, 0);
        if chunks.contains_key(&left_pos) {
            chunk.left_chunk_position = Some(left_pos);
        }
        let right_pos = chunk.position + IVec3::new(1, 0, 0);
        if chunks.contains_key(&right_pos) {
            chunk.right_chunk_position = Some(right_pos);
        }
        let front_pos = chunk.position + IVec3::new(0, 0, 1);
        if chunks.contains_key(&front_pos) {
            chunk.front_chunk_position = Some(front_pos);
        }
        let back_pos = chunk.position - IVec3::new(0, 0, 1);
        if chunks.contains_key(&back_pos) {
            chunk.back_chunk_position = Some(back_pos);
        }
        let up_pos = chunk.position + IVec3::new(0, 1, 0);
        if chunks.contains_key(&up_pos) {
            chunk.up_chunk_position = Some(up_pos);
        }
        let down_pos = chunk.position - IVec3::new(0, 1, 0);
        if chunks.contains_key(&down_pos) {
            chunk.down_chunk_position = Some(down_pos);
        }
    }

    pub fn generate_mesh(&mut self, chunks: &HashMap<IVec3, Chunk>) {
        // Extract adjacent chunk data to avoid borrowing conflicts
        let down_chunk_data = self.down_chunk_position
            .and_then(|pos| chunks.get(&pos))
            .map(|chunk| chunk.data);
        let up_chunk_data = self.up_chunk_position
            .and_then(|pos| chunks.get(&pos))
            .map(|chunk| chunk.data);
        let left_chunk_data = self.left_chunk_position
            .and_then(|pos| chunks.get(&pos))
            .map(|chunk| chunk.data);
        let right_chunk_data = self.right_chunk_position
            .and_then(|pos| chunks.get(&pos))
            .map(|chunk| chunk.data);
        let front_chunk_data = self.front_chunk_position
            .and_then(|pos| chunks.get(&pos))
            .map(|chunk| chunk.data);
        let back_chunk_data = self.back_chunk_position
            .and_then(|pos| chunks.get(&pos))
            .map(|chunk| chunk.data);

        self.generate_mesh_with_adjacent_data(
            down_chunk_data,
            up_chunk_data,
            left_chunk_data,
            right_chunk_data,
            front_chunk_data,
            back_chunk_data,
        );
    }

    pub fn generate_mesh_with_adjacent_data(
        &mut self,
        down_chunk_data: Option<[[[u8; 32]; 32]; 32]>,
        up_chunk_data: Option<[[[u8; 32]; 32]; 32]>,
        left_chunk_data: Option<[[[u8; 32]; 32]; 32]>,
        right_chunk_data: Option<[[[u8; 32]; 32]; 32]>,
        front_chunk_data: Option<[[[u8; 32]; 32]; 32]>,
        back_chunk_data: Option<[[[u8; 32]; 32]; 32]>,
    ) {
        let mut positions = Vec::new();
        let mut normals = Vec::new();
        let mut uvs = Vec::new();
        let mut indices = Vec::new();
        
        for (x, column) in self.data.iter().enumerate() {
            for (y, row) in column.iter().enumerate() {
                for (z, block) in row.iter().enumerate() {
                    if *block == 0 {
                        continue;
                    }
                    
                    let base_position = Vec3::new(x as f32, y as f32, z as f32);

                    //if there's not a block below, render the bottom face
                    //use the down_chunk_data to check the chunk below if y == 0
                    if (y == 0 && self.down_chunk_position.is_some() &&
                        down_chunk_data.map_or(true, |data| data[x][31][z] == 0))
                        || (y > 0 && self.data[x][y - 1][z] == 0) {
                        //render bottom face
                        let start_index = positions.len() as u32;
                        positions.extend_from_slice(&[
                            base_position,
                            base_position + Vec3::new(1.0, 0.0, 0.0),
                            base_position + Vec3::new(1.0, 0.0, 1.0),
                            base_position + Vec3::new(0.0, 0.0, 1.0),
                        ]);
                        normals.extend_from_slice(&[
                            Vec3::NEG_Y,
                            Vec3::NEG_Y,
                            Vec3::NEG_Y,
                            Vec3::NEG_Y,
                        ]);
                        uvs.extend_from_slice(&[
                            Vec2::new(0.0, 0.0),
                            Vec2::new(1.0, 0.0),
                            Vec2::new(1.0, 1.0),
                            Vec2::new(0.0, 1.0),
                        ]);
                        indices.extend_from_slice(&[
                            start_index, start_index + 1, start_index + 2,
                            start_index + 2, start_index + 3, start_index,
                        ]);
                    }

                    //if there's not a block above, render the top face
                    //use the up_chunk_data to check the chunk above if y == 31
                    if (y == 31 && self.up_chunk_position.is_some() &&
                        up_chunk_data.map_or(true, |data| data[x][0][z] == 0))
                        || (y < 31 && self.data[x][y + 1][z] == 0) {
                        //render top face
                        let start_index = positions.len() as u32;
                        let top_position = base_position + Vec3::new(0.0, 1.0, 0.0);
                        positions.extend_from_slice(&[
                            top_position + Vec3::new(0.0, 0.0, 1.0),
                            top_position + Vec3::new(1.0, 0.0, 1.0),
                            top_position + Vec3::new(1.0, 0.0, 0.0),
                            top_position,
                        ]);
                        normals.extend_from_slice(&[
                            Vec3::Y,
                            Vec3::Y,
                            Vec3::Y,
                            Vec3::Y,
                        ]);
                        uvs.extend_from_slice(&[
                            Vec2::new(0.0, 0.0),
                            Vec2::new(1.0, 0.0),
                            Vec2::new(1.0, 1.0),
                            Vec2::new(0.0, 1.0),
                        ]);
                        indices.extend_from_slice(&[
                            start_index, start_index + 1, start_index + 2,
                            start_index + 2, start_index + 3, start_index,
                        ]);
                    }

                    //if there's not a block to the left, render the left face
                    //use the left_chunk_data to check the chunk to the left if x == 0
                    if (x == 0 && self.left_chunk_position.is_some() &&
                        left_chunk_data.map_or(true, |data| data[31][y][z] == 0))
                        || (x > 0 && self.data[x - 1][y][z] == 0) {
                        //render left face
                        let start_index = positions.len() as u32;
                        positions.extend_from_slice(&[
                            base_position + Vec3::new(0.0, 0.0, 1.0),
                            base_position,
                            base_position + Vec3::new(0.0, 1.0, 0.0),
                            base_position + Vec3::new(0.0, 1.0, 1.0),
                        ]);
                        normals.extend_from_slice(&[
                            Vec3::NEG_X,
                            Vec3::NEG_X,
                            Vec3::NEG_X,
                            Vec3::NEG_X,
                        ]);
                        uvs.extend_from_slice(&[
                            Vec2::new(0.0, 0.0),
                            Vec2::new(1.0, 0.0),
                            Vec2::new(1.0, 1.0),
                            Vec2::new(0.0, 1.0),
                        ]);
                        indices.extend_from_slice(&[
                            start_index, start_index + 1, start_index + 2,
                            start_index + 2, start_index + 3, start_index,
                        ]);
                    }

                    //if there's not a block to the right, render the right face
                    //use the right_chunk_data to check the chunk to the right if x == 31
                    if (x == 31 && self.right_chunk_position.is_some() &&
                        right_chunk_data.map_or(true, |data| data[0][y][z] == 0))
                        || (x < 31 && self.data[x + 1][y][z] == 0) {
                        //render right face
                        let start_index = positions.len() as u32;
                        let right_position = base_position + Vec3::new(1.0, 0.0, 0.0);
                        positions.extend_from_slice(&[
                            right_position,
                            right_position + Vec3::new(0.0, 0.0, 1.0),
                            right_position + Vec3::new(0.0, 1.0, 1.0),
                            right_position + Vec3::new(0.0, 1.0, 0.0),
                        ]);
                        normals.extend_from_slice(&[
                            Vec3::X,
                            Vec3::X,
                            Vec3::X,
                            Vec3::X,
                        ]);
                        uvs.extend_from_slice(&[
                            Vec2::new(0.0, 0.0),
                            Vec2::new(1.0, 0.0),
                            Vec2::new(1.0, 1.0),
                            Vec2::new(0.0, 1.0),
                        ]);
                        indices.extend_from_slice(&[
                            start_index, start_index + 1, start_index + 2,
                            start_index + 2, start_index + 3, start_index,
                        ]);
                    }

                    //if there's not a block to the front, render the front face
                    //use the front_chunk_data to check the chunk to the front if z == 0
                    if (z == 0 && self.front_chunk_position.is_some() &&
                        front_chunk_data.map_or(true, |data| data[x][y][31] == 0))
                        || (z > 0 && self.data[x][y][z - 1] == 0) {
                        //render front face
                        let start_index = positions.len() as u32;
                        positions.extend_from_slice(&[
                            base_position,
                            base_position + Vec3::new(0.0, 1.0, 0.0),
                            base_position + Vec3::new(1.0, 1.0, 0.0),
                            base_position + Vec3::new(1.0, 0.0, 0.0),
                        ]);
                        normals.extend_from_slice(&[
                            Vec3::NEG_Z,
                            Vec3::NEG_Z,
                            Vec3::NEG_Z,
                            Vec3::NEG_Z,
                        ]);
                        uvs.extend_from_slice(&[
                            Vec2::new(0.0, 0.0),
                            Vec2::new(0.0, 1.0),
                            Vec2::new(1.0, 1.0),
                            Vec2::new(1.0, 0.0),
                        ]);
                        indices.extend_from_slice(&[
                            start_index, start_index + 1, start_index + 2,
                            start_index + 2, start_index + 3, start_index,
                        ]);
                    }

                    //if there's not a block to the back, render the back face
                    //use the back_chunk_data to check the chunk to the back if z == 31
                    if (z == 31 && self.back_chunk_position.is_some() &&
                        back_chunk_data.map_or(true, |data| data[x][y][0] == 0))
                        || (z < 31 && self.data[x][y][z + 1] == 0) {
                        //render back face
                        let start_index = positions.len() as u32;
                        let back_position = base_position + Vec3::new(0.0, 0.0, 1.0);
                        positions.extend_from_slice(&[
                            back_position + Vec3::new(1.0, 0.0, 0.0),
                            back_position + Vec3::new(1.0, 1.0, 0.0),
                            back_position + Vec3::new(0.0, 1.0, 0.0),
                            back_position,
                        ]);
                        normals.extend_from_slice(&[
                            Vec3::Z,
                            Vec3::Z,
                            Vec3::Z,
                            Vec3::Z,
                        ]);
                        uvs.extend_from_slice(&[
                            Vec2::new(0.0, 0.0),
                            Vec2::new(0.0, 1.0),
                            Vec2::new(1.0, 1.0),
                            Vec2::new(1.0, 0.0),
                        ]);
                        indices.extend_from_slice(&[
                            start_index, start_index + 1, start_index + 2,
                            start_index + 2, start_index + 3, start_index,
                        ]);
                    }
                }
            }
        }

        let mut mesh = Mesh::new(
            PrimitiveTopology::TriangleList,
            RenderAssetUsages::MAIN_WORLD | RenderAssetUsages::RENDER_WORLD,
        );
        mesh.insert_attribute(Mesh::ATTRIBUTE_POSITION, positions);
        mesh.insert_attribute(Mesh::ATTRIBUTE_NORMAL, normals);
        mesh.insert_attribute(Mesh::ATTRIBUTE_UV_0, uvs);
        mesh.insert_indices(bevy::render::mesh::Indices::U32(indices));

        self.mesh = Some(mesh);
    }
}

