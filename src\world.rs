use bevy::platform::collections::HashMap;
use bevy::prelude::*;

use crate::chunk::*;

#[derive(Resource)]
pub struct World {
    chunks: HashMap<IVec3, Chunk>,
    rendered_chunk_entities: HashMap<IVec3, Entity>,
    rendered_chunk_meshes : HashMap<IVec3, <PERSON>le<Mesh>>,
    player_position: IVec3,
    render_distance: u32,
    seed: u32,
}

pub struct WorldPlugin;

impl Plugin for WorldPlugin {
    fn build(&self, app: &mut App) {
        app.insert_resource(World {
            chunks: HashMap::new(),
            rendered_chunk_entities: HashMap::new(),
            rendered_chunk_meshes: HashMap::new(),
            player_position: IVec3::ZERO,
            render_distance: 4,
            seed: 42,
        })
        .add_systems(Update, world_render_loop);
    }
}

fn world_render_loop(
    mut world: ResMut<World>, 
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    world.render_loop();

    //spawn chunks
    let mut new_entities = Vec::new();
    let mut new_meshes = Vec::new();

    let rendered_chunks = world.rendered_chunk_entities.keys().copied().collect::<Vec<_>>();

    for (_, chunk) in world.chunks.iter_mut() {
        if !rendered_chunks.contains(&chunk.get_position()) {
            if let Some(mesh) = chunk.get_mesh() {
                let mesh_handle = meshes.add(mesh);
                //world.rendered_chunks.insert(mesh_handle.id(), chunk.get_position());
                let entity = commands.spawn((
                    Mesh3d(mesh_handle.clone_weak()),
                    MeshMaterial3d(materials.add(Color::WHITE)),
                    Transform::from_translation(chunk.get_position().as_vec3() * 32.0),
                ));

                let chunk_position = chunk.get_position();
                let entity_id = entity.id();

                new_entities.push((chunk_position, entity_id));
                new_meshes.push((chunk_position, mesh_handle));

                println!("Spawned chunk at {:?}", chunk_position);
            }
        }
    }

    // Insert the collected data after the loop
    for (chunk_position, entity_id) in new_entities {
        world.rendered_chunk_entities.insert(chunk_position, entity_id);
    }
    for (chunk_position, mesh_handle) in new_meshes {
        world.rendered_chunk_meshes.insert(chunk_position, mesh_handle);
    }

    //despawn chunks
    for position in world.rendered_chunk_entities.keys().copied().collect::<Vec<_>>() {
        if !world.chunks.contains_key(&position) {
            let mesh_id = world.rendered_chunk_meshes.remove(&position).unwrap();
            commands.entity(world.rendered_chunk_entities.remove(&position).unwrap()).despawn();
            meshes.remove(&mesh_id);

            println!("Despawned chunk at {:?}", position);
        }
    }
}

impl World {
    pub fn get_chunk(&self, position: IVec3) -> Option<&Chunk> {
        self.chunks.get(&position)
    }

    fn render_loop(&mut self) {
        let next_chunk_to_render = self.get_next_chunk_to_render();

        if let Some(next_chunk_to_render) = next_chunk_to_render {
            let mut new_chunk = Chunk::generate_chunk(next_chunk_to_render, self.seed);
            Chunk::add_adjacent_chunk_pointers(&mut new_chunk, &self.chunks);

            self.chunks.insert(next_chunk_to_render, new_chunk);
        }

        //remove chunks that are outside the render distance
        let render_distance_i32 = self.render_distance as i32;
        let start_pos = self.player_position / 32 - IVec3::splat(render_distance_i32);
        let end_pos = self.player_position / 32 + IVec3::splat(render_distance_i32);
        self.chunks.retain(|pos, _| pos.x >= start_pos.x && pos.x < end_pos.x && pos.y >= start_pos.y && pos.y < end_pos.y && pos.z >= start_pos.z && pos.z < end_pos.z);

        //pick a chunk to generate a mesh for
        let chunk_position_to_mesh = self.chunks.iter()
            .find(|(_, chunk)| !chunk.has_mesh())
            .map(|(pos, _)| *pos);

        if let Some(pos) = chunk_position_to_mesh {
            // Extract adjacent chunk data first to avoid borrowing conflicts
            let chunk_ref = self.chunks.get(&pos);
            if let Some(chunk_ref) = chunk_ref {
                let (down_pos, up_pos, left_pos, right_pos, front_pos, back_pos) = chunk_ref.get_adjacent_positions();

                let down_chunk_data = down_pos
                    .and_then(|pos| self.chunks.get(&pos))
                    .map(|chunk| chunk.get_data());
                let up_chunk_data = up_pos
                    .and_then(|pos| self.chunks.get(&pos))
                    .map(|chunk| chunk.get_data());
                let left_chunk_data = left_pos
                    .and_then(|pos| self.chunks.get(&pos))
                    .map(|chunk| chunk.get_data());
                let right_chunk_data = right_pos
                    .and_then(|pos| self.chunks.get(&pos))
                    .map(|chunk| chunk.get_data());
                let front_chunk_data = front_pos
                    .and_then(|pos| self.chunks.get(&pos))
                    .map(|chunk| chunk.get_data());
                let back_chunk_data = back_pos
                    .and_then(|pos| self.chunks.get(&pos))
                    .map(|chunk| chunk.get_data());

                // Now get mutable reference and generate mesh
                if let Some(chunk) = self.chunks.get_mut(&pos) {
                    chunk.generate_mesh_with_adjacent_data(
                        down_chunk_data,
                        up_chunk_data,
                        left_chunk_data,
                        right_chunk_data,
                        front_chunk_data,
                        back_chunk_data,
                    );
                }
            }
        }
    }

    fn get_next_chunk_to_render(&mut self) -> Option<IVec3> {
        //TODO: also take into account look direction

        let current_chunk_pos = self.player_position / 32;

        let render_distance_i32 = self.render_distance as i32;
        let start_pos = current_chunk_pos - IVec3::splat(render_distance_i32);
        let end_pos = current_chunk_pos + IVec3::splat(render_distance_i32);

        let mut blank_chunks: Vec<IVec3> = Vec::new();

        //loop through all positions in between start and end pos
        for x in start_pos.x..end_pos.x {
            for y in start_pos.y..end_pos.y {
                for z in start_pos.z..end_pos.z {
                    //if chunk is not in chunks
                    if !self.chunks.contains_key(&IVec3::new(x, y, z)) {
                        blank_chunks.push(IVec3::new(x, y, z));
                    }
                }
            }
        }

        //sort by proximity to player position
        blank_chunks.sort_by_key(|k| (k - current_chunk_pos).length_squared() as i32);

        //return the first one
        if blank_chunks.is_empty() {
            None
        } else {
            Some(blank_chunks[0])
        }
    }

    pub fn set_player_position(&mut self, position: IVec3) {
        self.player_position = position;
    }
}

