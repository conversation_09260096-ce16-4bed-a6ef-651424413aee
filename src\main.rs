use bevy::prelude::*;

mod player;
mod world;
mod chunk;

fn main() {
    App::new()
        .add_systems(Startup, setup)
        .add_plugins(DefaultPlugins)
        .add_plugins(player::PlayerPlugin)
        .add_plugins(world::WorldPlugin)
        .run();
}


fn setup(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    /*commands.spawn((
        PointLight {
            shadows_enabled: true,
            ..default()
        },
        Transform::from_xyz(4.0, 8.0, 4.0),
    ));*/
    commands.spawn((DirectionalLight::default(), Transform::from_xyz(0.0, 2.0, 0.0).looking_at(Vec3::new(-1.0, -3.0, -1.0), Vec3::Y)));
}